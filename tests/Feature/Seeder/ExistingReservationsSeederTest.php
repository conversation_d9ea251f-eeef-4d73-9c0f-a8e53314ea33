<?php

namespace Tests\Feature;

use App\Models\Field;
use App\Models\Reservation;
use App\Models\User;
use App\Models\Utility;
use Database\Seeders\ExistingReservationsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

#[CoversClass(ExistingReservationsSeeder::class)]
class ExistingReservationsSeederTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create required dependencies
        $this->createTestDependencies();
    }

    #[Test]
    public function it_can_seed_reservations_with_updated_dates()
    {
        // Clear any existing reservations
        Reservation::query()->delete();

        // Run the seeder
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->expectsQuestion('Do you want to clear existing reservations before seeding?', 'no')
            ->assertExitCode(0);

        // Verify reservations were created
        $this->assertGreaterThan(0, Reservation::count());

        // Verify dates are adjusted relative to the original earliest date
        $earliestReservation = Reservation::orderBy('booking_date')->first();
        $latestReservation = Reservation::orderBy('booking_date', 'desc')->first();

        // The date range should be preserved (original range was 10 days: 2025-08-10 to 2025-08-20)
        $daysDifference = $earliestReservation->booking_date->diffInDays($latestReservation->booking_date);
        $this->assertEquals(10, $daysDifference);
    }

    #[Test]
    public function it_preserves_reservation_relationships()
    {
        // Clear any existing reservations
        Reservation::query()->delete();

        // Run the seeder
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->expectsQuestion('Do you want to clear existing reservations before seeding?', 'no')
            ->assertExitCode(0);

        // Get a reservation with utilities
        $reservationWithUtilities = Reservation::with(['field', 'user', 'utilities'])
            ->whereHas('utilities')
            ->first();

        if ($reservationWithUtilities) {
            // Verify relationships are properly loaded
            $this->assertNotNull($reservationWithUtilities->field);
            $this->assertNotNull($reservationWithUtilities->user);
            $this->assertGreaterThan(0, $reservationWithUtilities->utilities->count());

            // Verify utility pivot data is preserved
            $utility = $reservationWithUtilities->utilities->first();
            $this->assertNotNull($utility->pivot->hours);
            $this->assertNotNull($utility->pivot->rate);
            $this->assertNotNull($utility->pivot->cost);
        }
    }

    #[Test]
    public function it_prevents_duplicate_reservations()
    {
        // Clear any existing reservations
        Reservation::query()->delete();

        // Run the seeder twice
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->expectsQuestion('Do you want to clear existing reservations before seeding?', 'no')
            ->assertExitCode(0);

        $firstCount = Reservation::count();

        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->expectsQuestion('Do you want to clear existing reservations before seeding?', 'no')
            ->assertExitCode(0);

        $secondCount = Reservation::count();

        // Count should be the same (no duplicates created)
        $this->assertEquals($firstCount, $secondCount);
    }

    #[Test]
    public function it_can_clear_existing_reservations()
    {
        // Create some test reservations
        Reservation::factory()->count(3)->create();
        $this->assertGreaterThan(0, Reservation::count());

        // Run the seeder with clear option
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->expectsQuestion('Do you want to clear existing reservations before seeding?', 'yes')
            ->assertExitCode(0);

        // Verify new reservations were created (should be more than the 3 we started with)
        $this->assertGreaterThan(3, Reservation::count());
    }

    #[Test]
    public function it_handles_missing_dependencies_gracefully()
    {
        // Clear any existing reservations
        Reservation::query()->delete();

        // Remove a field that the seeder expects
        Field::where('name', 'Veld futbol')->delete();

        // Run the seeder - it should handle missing dependencies gracefully
        $this->artisan('db:seed', ['--class' => 'ExistingReservationsSeeder'])
            ->expectsQuestion('Do you want to clear existing reservations before seeding?', 'no')
            ->assertExitCode(0);

        // Some reservations should still be created (for fields that exist)
        $this->assertGreaterThan(0, Reservation::count());
    }

    /**
     * Create the required test dependencies
     */
    private function createTestDependencies(): void
    {
        // Create test user
        User::factory()->create([
            'email' => '<EMAIL>',
            'name' => 'Andy Janga',
        ]);

        // Create test fields
        $fields = [
            'Veld futbol',
            'Veld Bolas',
            'Veld Multi',
            'Patio Area',
        ];

        foreach ($fields as $fieldName) {
            Field::factory()->create([
                'name' => $fieldName,
                'hourly_rate' => 50.00,
                'night_hourly_rate' => 80.00,
            ]);
        }

        // Create test utilities
        $utilities = [
            'Paña di mesa',
            'Sta tafel',
            'Mesa rondo',
            'Stoel',
        ];

        foreach ($utilities as $utilityName) {
            Utility::factory()->create([
                'name' => $utilityName,
                'hourly_rate' => 15.00,
            ]);
        }
    }
}
